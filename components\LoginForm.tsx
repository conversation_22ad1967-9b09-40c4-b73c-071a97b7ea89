'use client'

import * as React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Github} from "lucide-react"
import { signIn } from "next-auth/react"
import Turnstile, { useTurnstile } from "react-turnstile"
import Link from "next/link"


export function LoginForm() {
  const [error, setError] = React.useState<string | null>(null);
  const [isCredentialsLoading, setIsCredentialsLoading] = React.useState(false);
  const [isGitHubLoading, setIsGitHubLoading] = React.useState(false);
  const [isRegisterDialogOpen, setIsRegisterDialogOpen] = React.useState(false); // 新增 state
  const [turnstileToken, setTurnstileToken] = React.useState<string | null>(null);
  const turnstile = useTurnstile(); // 将 useTurnstile 移到组件顶层

  const handleCredentialsSignIn = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setError(null)
    setIsCredentialsLoading(true)
    const formData = new FormData(event.currentTarget)
    const email = formData.get('email') as string
    const password = formData.get('password') as string
    try {
      const result = await signIn('credentials', {
        email,
        password,
        token: turnstileToken,
        redirect: false,
      })
      console.log(result)
      if (result?.error) {
        if (result.error === "CredentialsSignin") {
          setError("邮箱或密码无效。")
        } else {
          setError(result.error)
        }
      } 
    } catch (err) {
      setError("登录时发生意外错误。")
      console.error(err)
    } finally {
      setIsCredentialsLoading(false)
    }
  }

  const handleGitHubSignIn = async () => {
    setIsGitHubLoading(true)
    setError(null)
    try {
      // GitHub 登录通常涉及完整页面重定向
      // callbackUrl 设置为当前页面，以便登录后返回
      await signIn('github', { callbackUrl: window.location.href })
      // 如果 signIn 启动重定向，下面的代码可能不会立即执行
    } catch (err) {
      setError("GitHub 登录时发生意外错误。")
      console.error(err)
      setIsGitHubLoading(false) // 仅在 signIn 本身在重定向前抛出错误时设置
    }
    // 如果发生重定向，组件将在页面重新加载时重新初始化，因此无需在此处设置 setIsGitHubLoading(false)
  }

  return (
    <>
      <div className=" w-full sm:w-[400px] mx-auto ">
          
        <div className="space-y-4 py-4">
          {error && (
            <div className="bg-destructive/15 p-3 rounded-md flex items-center gap-x-2 text-sm text-destructive">
              {/* 可以添加一个错误图标 */}
              <p>{error}</p>
            </div>
          )}
          <form onSubmit={handleCredentialsSignIn} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email-dialog">Email</Label>
              <Input 
                id="email-dialog" 
                name="email" 
                type="email" 
                placeholder="<EMAIL>"
                required
                disabled={isCredentialsLoading || isGitHubLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password-dialog">Password</Label>
              <Input
                id="password-dialog"
                name="password"
                type="password"
                required
                disabled={isCredentialsLoading || isGitHubLoading}
              />
            </div>
            <Turnstile
              sitekey={'0x4AAAAAABPToBobzrFbjtye'}
              onVerify={(token) => setTurnstileToken(token)}
            />
            <Button type="submit" className="w-full cursor-pointer" disabled={isCredentialsLoading || isGitHubLoading}>
              {isCredentialsLoading ? "登录中..." : "Sign In"}
            </Button>
          </form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                或使用以下方式登录
              </span>
            </div>
          </div>

          <Button 
            onClick={handleGitHubSignIn} 
            className="w-full cursor-pointer"
            variant="outline"
            disabled={isGitHubLoading || isCredentialsLoading}
          >
            <Github className="mr-2 h-4 w-4" />
            {isGitHubLoading ? "跳转中..." : "Sign in with GitHub"}
          </Button>
        </div>
        <div className="flex flex-col space-y-2 text-center text-sm text-muted-foreground sm:flex-row sm:justify-center">
          <div>
            没有帐户？
            <Link href="/sign-up">
            <Button
              variant="link"
              className="p-0 h-auto underline cursor-pointer"
              onClick={() => {
                setIsRegisterDialogOpen(true); // 打开注册对话框
              }}
            >
              注册  
            </Button>
            </Link>
          </div>  
        </div>
      </div>
  </>
  )
}