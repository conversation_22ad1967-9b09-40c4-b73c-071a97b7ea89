'use server'

import { enhanceUserPrompt } from "@/lib/ai/image";
import { prisma } from "@/lib/prisma";

export async function createEditor(userId: string, parameters: any, session: any) {
    try {
    // Extract user ID from session
        const user = await prisma.user.findUnique({
          where: { id: userId }
        });
        
        if (!user) {
          return {status: 'failed', error: "User not found" };
        }
    
        // Extract parameters from the request body
    
        
      //   // Find the model to get credit cost
        const model = await prisma.model.findUnique({
          where: { id: parameters.modelId }
        });
        
        if (!model) {
          return {status: 'failed', error: "Model not found" };
        }
      const userSubscription = await prisma.subscription.findUnique({
        where: { userId: user.id }
      });
      if (!userSubscription) {
        return {status: 'failed', error: "User subscription not found" };
      }
      //calculate the credit cost
      const creditCost = model.creditCost * parameters.num_images;
      if (userSubscription.creditsRemaining < creditCost) {
        return {status: 'failed', error: "Not enough credits" };
      }
      //Enhance the user prompt
      const enhancePrompt = await enhanceUserPrompt(parameters.prompt);
      if (enhancePrompt.error) {
        return {status: 'failed', error: enhancePrompt.error };
      }
    
      //update the user subscription
      const updatedSubscription = await prisma.subscription.update({
        where: { userId: user.id },
        data: { 
          creditsRemaining: {
            decrement: creditCost
          }
        }
      });
      //   // Create image editor record
        const imageEditor = await prisma.editor.create({
          data: {
            userId: user.id,
            userPrompt: parameters.prompt,
            prompt: enhancePrompt.prompt as string,
            modelName: model.modelName,
            modelId: model.id,  
            parameters,
            status: "PENDING",
            creditsUsed: creditCost
          }
        });
        const webhook = `${process.env.SITE_URL}/api/callback/kontext`;
    
      //   // Add generation ID to the request headers to track it
       const responseApi = await fetch(`https://queue.fal.run/${model.modelId}?fal_webhook=${webhook}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Key ${process.env.FAL_KEY}`
          },
          body: JSON.stringify({
            prompt: enhancePrompt.prompt as string,
            image_url: parameters.image_url,
            num_images: parameters.num_images,
            aspect_ratio: parameters.aspect_ratio,
            seed: parameters.seed,
            guidance_scale: parameters.guidance_scale,
            sync_mode: false,
            output_format: parameters.output_format,
            safety_tolerance: 2,
          })
        });
      const responseData = await responseApi.json();
    
      //   // Update the image editor record with the task ID
        if (responseData.request_id) {
          await prisma.editor.update({
            where: { id: imageEditor.id },
            data: { taskId: responseData.request_id, status: "PENDING", metadata: responseData }
          });
        } else {
          await prisma.editor.update({
            where: { id: imageEditor.id },
            data: { status: "FAILED", metadata: responseData }
          });
        }
        
      //   return response and user remaining credits 
      return { status: 'ok', taskId: imageEditor.id, credits: updatedSubscription.creditsRemaining};
      
      } catch (error) {
        console.error("Error in FAL proxy:", error);
        return {status: 'failed', error: "Internal Server Error" };
      }
}