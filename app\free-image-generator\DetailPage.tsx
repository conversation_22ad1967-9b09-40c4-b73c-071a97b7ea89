'use client';

import * as React from 'react';
import { motion } from "framer-motion";
import Image from 'next/image';
import { <PERSON>R<PERSON>, Lightbulb, Palette, Layout, ImageIcon, Sparkles, Zap } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// 修复 JSX 命名空间问题
declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}

// 定义动画变体类型
type AnimationVariants = {
  hidden: {
    opacity: number;
    y: number;
    transition?: {
      duration?: number;
      ease?: string;
      staggerChildren?: number;
    };
  };
  visible: {
    opacity: number;
    y: number;
    transition: {
      duration: number;
      ease: string;
      staggerChildren?: number;
    };
  };
};

// 定义动画变体
const sectionVariants: AnimationVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: { 
    opacity: 1, 
    y: 0, 
    transition: { 
      duration: 0.6, 
      ease: "easeOut", 
      staggerChildren: 0.2 
    } 
  },
};

const itemVariants: AnimationVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0, 
    transition: { 
      duration: 0.5, 
      ease: "easeOut" 
    } 
  },
};

const DetailPage: React.FC = () => {
  return (
    <>
      {/* 提示词技巧 */}
        {/* 提示词技巧 */}
        {/* Hero Section */}
      <section className="pt-32 pb-16 bg-[#F5F5F5]">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
              Master the Magic of AI Drawing: Prompt Techniques to Create Stunning Works
            </h1>
            <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed text-[#333333]">
              Want to let AI understand your creativity and generate stunning images? The key lies in carefully crafting your "prompt" (Prompt). A good prompt is like giving the AI painter detailed instructions, guiding it to create works closer to your imagination.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold mb-8 text-center">Core Elements of Prompt Writing</h2>

            <div className="grid md:grid-cols-2 gap-8">
              {/* Element 1: Subject */}
              <Card className="border border-[#E0E0E0] overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  <div className="bg-[#121212] p-4 flex items-center">
                    <ImageIcon className="w-6 h-6 text-white mr-2" />
                    <h3 className="text-xl font-semibold text-white">Subject</h3>
                  </div>
                  <div className="p-6">
                    <p className="text-[#333333] mb-4">Clearly describe the core object or scene you want to depict.</p>
                    <div className="bg-[#F5F5F5] p-4 rounded-lg mb-4">
                      <p className="text-[#666666] text-sm mb-2">❌ Vague Prompt:</p>
                      <p className="font-medium">"animal"</p>
                    </div>
                    <div className="bg-[#F5F5F5] p-4 rounded-lg">
                      <p className="text-[#666666] text-sm mb-2">✅ Clear Prompt:</p>
                      <p className="font-medium">"a cat wearing a spacesuit"</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Element 2: Details */}
              <Card className="border border-[#E0E0E0] overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  <div className="bg-[#121212] p-4 flex items-center">
                    <Sparkles className="w-6 h-6 text-white mr-2" />
                    <h3 className="text-xl font-semibold text-white">Details & Adjectives</h3>
                  </div>
                  <div className="p-6">
                    <p className="text-[#333333] mb-4">Use descriptive words to describe the features, colors, textures, emotions, etc. of the object.</p>
                    <div className="bg-[#F5F5F5] p-4 rounded-lg mb-4">
                      <p className="text-[#666666] text-sm mb-2">❌ Lack of details:</p>
                      <p className="font-medium">"a cat"</p>
                    </div>
                    <div className="bg-[#F5F5F5] p-4 rounded-lg">
                      <p className="text-[#666666] text-sm mb-2">✅ Clear details:</p>
                      <p className="font-medium">"a fluffy, curious orange cat"</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Element 3: Style */}
              <Card className="border border-[#E0E0E0] overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  <div className="bg-[#121212] p-4 flex items-center">
                    <Palette className="w-6 h-6 text-white mr-2" />
                    <h3 className="text-xl font-semibold text-white">Artistic Style</h3>
                  </div>
                  <div className="p-6">
                    <p className="text-[#333333] mb-4">Tell AI what artistic style you expect.</p>
                    <div className="bg-[#F5F5F5] p-4 rounded-lg">
                      <p className="text-[#666666] text-sm mb-2">✅ Style Examples:</p>
                      <ul className="list-disc list-inside space-y-2">
                        <li>"Impressionist oil painting"</li>
                        <li>"Cyberpunk cityscape at night"</li>
                        <li>"Studio Ghibli-style landscape"</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Element 4: Composition */}
              <Card className="border border-[#E0E0E0] overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  <div className="bg-[#121212] p-4 flex items-center">
                    <Layout className="w-6 h-6 text-white mr-2" />
                    <h3 className="text-xl font-semibold text-white">Composition & Angle</h3>
                  </div>
                  <div className="p-6">
                    <p className="text-[#333333] mb-4">Describe the layout and perspective of the scene.</p>
                    <div className="bg-[#F5F5F5] p-4 rounded-lg">
                      <p className="text-[#666666] text-sm mb-2">✅ Composition Examples:</p>
                      <ul className="list-disc list-inside space-y-2">
                        <li>"wide-angle shot of a mountain range"</li>
                        <li>"close-up portrait of a person"</li>
                        <li>"bird's-eye view of city streets"</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Element 5: Lighting */}
              <Card className="border border-[#E0E0E0] overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  <div className="bg-[#121212] p-4 flex items-center">
                    <Lightbulb className="w-6 h-6 text-white mr-2" />
                    <h3 className="text-xl font-semibold text-white">Lighting & Mood</h3>
                  </div>
                  <div className="p-6">
                    <p className="text-[#333333] mb-4">Describe the lighting conditions and overall mood.</p>
                    <div className="bg-[#F5F5F5] p-4 rounded-lg">
                      <p className="text-[#666666] text-sm mb-2">✅ Lighting Examples:</p>
                      <ul className="list-disc list-inside space-y-2">
                        <li>"evening soft light"</li>
                        <li>"mysterious foggy forest"</li>
                        <li>"bright sunlight on the beach"</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Element 6: Artist Styles */}
              <Card className="border border-[#E0E0E0] overflow-hidden hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-0">
                  <div className="bg-[#121212] p-4 flex items-center">
                    <Zap className="w-6 h-6 text-white mr-2" />
                    <h3 className="text-xl font-semibold text-white">Artist Styles</h3>
                  </div>
                  <div className="p-6">
                    <p className="text-[#333333] mb-4">If you like a particular artist's style, you can try mentioning it.</p>
                    <div className="bg-[#F5F5F5] p-4 rounded-lg">
                      <p className="text-[#666666] text-sm mb-2">✅ Artist Style Examples:</p>
                      <ul className="list-disc list-inside space-y-2">
                        <li>"Van Gogh's style stars"</li>
                        <li>"Dalí's Surrealist-style scene"</li>
                        <li>"Studio Ghibli-style fantasy creatures"</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>

          {/* Example Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-12"
          >
            <h2 className="text-2xl font-bold mb-6">Complete Prompt Examples</h2>

            <div className=" bg-[#F5F5F5] p-6 rounded-lg mb-8">
              <div>
                <p className="font-medium mb-2">Basic Prompt:</p>
                <p className="text-[#333333] mb-4">"cityscape at night"</p>

                <div className="w-full h-[500px] bg-[#E0E0E0] rounded-lg mb-6 flex items-center justify-center">
                  <img
                    src="https://pic.rapidmock.dev/a4f5dfde-5836-4a03-87e1-baa511801542-0-TtWsWdQZ0O598UXFZP71N.png"
                    alt="Basic prompt generated image"
                    className="h-full object-cover rounded-lg"
                  />
                </div>
              </div>  
              <div>             
                 <p className="font-medium mb-2">Optimized Prompt:</p>
                  <p className="text-[#333333] mb-4">
                    "futureist cyberpunk cityscape at night, neon lights reflecting on wet streets after rain, wide-angle lens, low-angle perspective, blue and purple color scheme, film grain, high detail"
                  </p>

                  <div className="w-full h-[500px] bg-[#E0E0E0] rounded-lg mb-6 flex items-center justify-center">
                    <img
                      src="https://pic.rapidmock.dev/769bd241-1b0e-4f77-8d6b-d4c409378409-0-Q6o5zC_XWxGkFAjWvqqoK.png"
                      alt="Optimized prompt generated image"
                      className="h-full object-cover rounded-lg"
                    />
                  </div>
              </div>
            </div>
          </motion.div>

          {/* Negative Prompts */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="mb-12"
          >
            <h2 className="text-2xl font-bold mb-6">Negative Prompts</h2>
            <p className="text-[#333333] mb-6">
              In "Advanced Settings", you can specify elements that you do not want to appear in the image, helping AI exclude distractions.
            </p>

            <div className="bg-[#F5F5F5] p-6 rounded-lg">
              <p className="font-medium mb-2">Negative Prompt Examples:</p>
              <ul className="list-disc list-inside space-y-2 text-[#333333]">
                <li>"blurry, deformed, low quality, pixelated"</li>
                <li>"overexposed, oversaturated, unnatural colors"</li>
                <li>"text, watermark, signature"</li>
                <li>"extra fingers, deformed limbs, incorrect anatomy"</li>
              </ul>
            </div>
          </motion.div>

          {/* Tips Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-12"
          >
            <div className="border-l-4 border-[#121212] pl-6 py-2">
              <h2 className="text-2xl font-bold mb-4">Prompt Writing Tips</h2>
              <ul className="space-y-3 text-[#333333]">
                <li>• Use commas to separate different concepts and descriptions</li>
                <li>• Keywords at the beginning will receive higher weight</li>
                <li>• Try different combinations, record good prompts</li>
                <li>• Keep it simple but specific, avoid overly long descriptions</li>
                <li>• Using English prompts usually yields better results</li>
              </ul>
            </div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-center"
          >
            <p className="text-lg mb-6">  Keep trying and adjusting your prompts, unleash your imagination, and unlock the infinite possibilities of AI painting!</p>
            <Button
              size="lg"
              className="bg-[#121212] text-white hover:bg-[#333333] hover:scale-105 transition-all duration-300"
            >
              Start Creating Now
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </motion.div>
        </div>
      </section>
          {/* 案例展示 */}
          <motion.div // 4. 应用 motion.div
            id="showcases"
            className="py-10 md:py-16 bg-slate-50 px-4"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.1 }} 
            variants={sectionVariants} 
          >
            <motion.h2 className="text-2xl md:text-3xl font-bold text-center mb-8 text-gray-800" variants={itemVariants}> 
              Inspiration Gallery: Explore the Visual Marvels of AI
            </motion.h2>
            <motion.p className="text-base md:text-lg text-center text-gray-600 max-w-2xl mx-auto mb-10" variants={itemVariants}> 
              See the stunning images created by our users and AI models. From dreamy scenes to surreal art, anything is possible.
            </motion.p>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto">
              <motion.div className="bg-white rounded-lg shadow-lg overflow-hidden group" variants={itemVariants}>
                <div className="aspect-square w-full overflow-hidden bg-gray-200">
                  <Image src="/case-1.webp" alt="AI generated astronaut on Mars" width={500} height={500} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
                </div>
                <div className="p-4">
                  <p className="text-xs text-gray-500 mb-1">Prompt:</p>
                  <p className="text-gray-700 font-medium text-sm leading-relaxed">"A astronaut stands on the red desert of Mars, far away is the Earth, epic feel, digital painting, detailed"</p>
                </div>
              </motion.div>
              <motion.div className="bg-white rounded-lg shadow-lg overflow-hidden group" variants={itemVariants}>
                <div className="aspect-square w-full overflow-hidden bg-gray-200">
                  <Image src="/case-2.webp" alt="AI generated magic forest" width={500} height={500} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
                </div>
                <div className="p-4">
                  <p className="text-xs text-gray-500 mb-1">Prompt:</p>
                  <p className="text-gray-700 font-medium text-sm leading-relaxed">"Deep into the magic forest, sunlight filtering through leaves, glowing mushrooms, fairy atmosphere, Studio Ghibli style"</p>
                </div>
              </motion.div>
              <motion.div className="bg-white rounded-lg shadow-lg overflow-hidden group" variants={itemVariants}>
                <div className="aspect-square w-full overflow-hidden bg-gray-200">
                  <Image src="/case-3.webp" alt="AI generated cyberpunk city" width={500} height={500} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
                </div>
                <div className="p-4">
                  <p className="text-xs text-gray-500 mb-1"> Prompt:</p>
                  <p className="text-gray-700 font-medium text-sm leading-relaxed">"Rainy cyberpunk city street, neon lights flashing, flying cars, skyscrapers, movie feel, 4K resolution"</p>
                </div>
              </motion.div>
            </div>
            <motion.p className="text-center mt-10 text-gray-600 text-base md:text-lg" variants={itemVariants}>
              Ready to create your own masterpiece? <a href="#image-generator-form" className="text-blue-600 hover:underline font-semibold">Start generating now!</a>
            </motion.p>
          </motion.div>

          <motion.div 
            id="testimonials"
            className="py-10 md:py-16 px-4"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.15 }} 
            variants={sectionVariants} 
          >
            <motion.h2 className="text-2xl md:text-3xl font-bold text-center mb-8 text-gray-800" variants={itemVariants}> 
              Listen to  what creators say
            </motion.h2>
            <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
              <motion.div className="bg-slate-100 p-6 rounded-lg shadow" variants={itemVariants}>
                <p className="text-gray-700 leading-relaxed italic text-sm md:text-base">
                  "This AI image generator is amazing! I'm a blogger who often needs images for my content. Previously, finding images was time-consuming, but now I can simply input my ideas, and within seconds, I get high-quality original images, significantly improving my productivity."
                </p>
                <p className="text-right mt-4 font-semibold text-gray-800 text-sm">- Xi Lin, lifestyle blogger</p>
              </motion.div>
              <motion.div className="bg-slate-100 p-6 rounded-lg shadow" variants={itemVariants}>   
                <p className="text-gray-700 leading-relaxed italic text-sm md:text-base">
                  "As an independent game developer, I always need to find game resources. This tool allows me to quickly generate concept images and some simple game materials, which is very helpful for verifying ideas and early prototype development. The interface is also very intuitive!"
                </p>
                <p className="text-right mt-4 font-semibold text-gray-800 text-sm">- Alex Chen, Independent Game Developer</p>
              </motion.div>
            </div>
          </motion.div>

          <motion.div 
            id="faq"
            className="py-10 md:py-16 px-4"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.1 }} 
            variants={sectionVariants} 
          >
            <motion.h2 className="text-2xl md:text-3xl font-bold text-center mb-8 text-gray-800" variants={itemVariants}> 
              FAQ
            </motion.h2>
            <div className="max-w-3xl mx-auto space-y-6">
              <motion.div className="bg-white p-5 rounded-lg shadow" variants={itemVariants}>
                <h3 className="font-semibold text-lg text-gray-800 mb-2">What kind of images can I generate with Photo AI?</h3>
                <p className="text-gray-600 leading-relaxed text-sm md:text-base">
                  Almost anything you can imagine! From realistic photos, cartoon illustrations, abstract art to specific styles of paintings (like oil paintings, watercolor, pixel art, etc.). You can describe people, animals, landscapes, objects, scenes, or even abstract concepts. Try different prompt combinations and explore infinite possibilities.
                </p>
              </motion.div>
              <motion.div className="bg-white p-5 rounded-lg shadow" variants={itemVariants}>
                <h3 className="font-semibold text-lg text-gray-800 mb-2">How long does it take to generate an image?</h3>
                <p className="text-gray-600 leading-relaxed text-sm md:text-base">
                  The generation time depends on the complexity of the prompt and the AI model used. Generally, it takes a few seconds to generate an image. For more complex prompts, it may take a bit longer.
                </p>
              </motion.div>
              <motion.div className="bg-white p-5 rounded-lg shadow" variants={itemVariants}>
                <h3 className="font-semibold text-lg text-gray-800 mb-2">Can the generated images be used for commercial purposes?</h3>
                <p className="text-gray-600 leading-relaxed text-sm md:text-base">
                  About the commercial rights of generated images, please refer to our <a href="/terms-of-use" className="text-blue-600 hover:underline">Terms of Use</a>. Generally, you own the rights to the images you generate, but specific details are subject to the Terms of Use.
                </p>
              </motion.div>
              <motion.div className="bg-white p-5 rounded-lg shadow" variants={itemVariants}>
                <h3 className="font-semibold text-lg text-gray-800 mb-2">How can I improve my image generation results?</h3>
                <p className="text-gray-600 leading-relaxed text-sm md:text-base">
                  The key lies in the quality of your prompt. Please refer to our <a href="#prompt-tips" className="text-blue-600 hover:underline">"Prompt Tips"</a> section to learn how to write more effective and descriptive prompts. Additionally, try adjusting the parameters in the advanced settings, such as steps and guidance strength, to improve the results.
                </p>
              </motion.div>
            </div>
          </motion.div>
        </>
    );
};

export default DetailPage;