'use client'

import * as React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { signUp } from "@/app/(login)/action" // 确保路径正确
import Link from "next/link"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import Turnstile from "react-turnstile"

export function RegisterForm() {
  const session = useSession()

  const [turnstileToken, setTurnstileToken] = React.useState<string | null>(null)
  const [isRegistering, setIsRegistering] = React.useState(false)
  const [registrationMessage, setRegistrationMessage] = React.useState<string | null>(null)
  const [isSuccess, setIsSuccess] = React.useState(false)
  const formRef = React.useRef<HTMLFormElement>(null);
  const router = useRouter()
  React.useEffect(() => {
    if (session.status === "authenticated") {
      router.push('/')
    }
  }, [session])

  const handleRegister = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setIsRegistering(true)
    setRegistrationMessage(null)
    setIsSuccess(false)

    const formData = new FormData(event.currentTarget)
    // 简单的前端密码匹配校验
    const password = formData.get('password') as string
    const confirmPassword = formData.get('confirmPassword') as string

    if (password !== confirmPassword) {
      setRegistrationMessage("密码和确认密码不匹配。")
      setIsRegistering(false)
      return
    }
    if (!turnstileToken) {
      setRegistrationMessage("请完成turnstile验证。")
      setIsRegistering(false)
      return
    }
    formData.set('token', turnstileToken)

    try {
      const result = await signUp(null, formData) // prevState 为 null

      if (result.status === 'ok') {
        setIsSuccess(true)
        setRegistrationMessage("注册成功！现在您可以登录了。")  
        formRef.current?.reset(); // 注册成功后清空表单
      } else if (result.status === 'failed') {
        setRegistrationMessage(result.error?.message || "注册失败，请重试。")
      } else {
        setRegistrationMessage("发生未知错误，请重试。")
      }
    } catch (error) {
      console.error("Registration error:", error)
      setRegistrationMessage("注册过程中发生意外错误。")
    } finally {
      setIsRegistering(false)
    }
  }


  // 当对话框关闭时，重置状态，以便下次打开是初始状态
  React.useEffect(() => {
    setRegistrationMessage(null);
    setIsSuccess(false);
    setIsRegistering(false);
    formRef.current?.reset();
  }, []);

  return (
   <div className="w-full sm:w-[400px] mx-auto ">
            
            {registrationMessage && (
              <div className={` text-center p-3 rounded-md text-sm my-4 ${isSuccess ? 'text-green-700' : 'text-destructive'}`}>
                {registrationMessage} <Link href="/sign-in" className="text-gray-500">登录</Link>
              </div>
            )}

        {!isSuccess && (
        <>
          <form ref={formRef} onSubmit={handleRegister} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email-register">Email</Label>
              <Input id="email-register" name="email" type="email" placeholder="<EMAIL>" required disabled={isRegistering} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password-register">Password</Label>
              <Input id="password-register" name="password" type="password" required disabled={isRegistering} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword-register">Confirm Password</Label>
              <Input id="confirmPassword-register" name="confirmPassword" type="password" required disabled={isRegistering} />
            </div>
            <div>
              <Turnstile
                sitekey={'0x4AAAAAABPToBobzrFbjtye'}
                onVerify={(token) => setTurnstileToken(token)}
              />  
            </div>
            <Button type="submit" className="w-full cursor-pointer" disabled={isRegistering}>
              {isRegistering ? "注册中..." : "Sign Up"}
            </Button>
          </form>
          </> 
          )}
        
          {!isSuccess && (
            <div className="text-center">
            已有帐户？
            <Link href="/sign-in">
              登录
            </Link>
            </div>
              )}
    </div>
  )
}