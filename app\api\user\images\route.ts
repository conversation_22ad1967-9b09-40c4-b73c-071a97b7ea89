import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';

// GET 获取用户所有图片生成记录
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 获取用户ID
    const userId = session.user.id;

    // 从数据库获取用户的所有图片生成记录
    const generations = await prisma.imageGeneration.findMany({
      where: {
        userId: userId
      },
      include: {
        model: true,
        images: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({ generations });
  } catch (error) {
    console.error('获取用户图片生成记录失败:', error);
    return NextResponse.json({ error: '获取图片生成记录失败' }, { status: 500 });
  }
}
