import { Twitter, Linkedin } from "lucide-react"
import Link from "next/link"

export default function Footer() {
  return (
    <footer className="bg-[#121212] text-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8 mb-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">ImageFox</h3>
            <div className="text-[#CCCCCC] mb-4">Creating endless possibilities By AI</div>
            <div className="flex space-x-4">
            <a href="#" className="text-[#CCCCCC] hover:text-white transition-colors">
              <Twitter size={20} />
            </a>
            <a href="#" className="text-[#CCCCCC] hover:text-white transition-colors">
              <Linkedin size={20} />
            </a>
          </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Products</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/free-image-generator" className="text-[#CCCCCC] hover:text-white transition-colors">
                  Free Image Generator
                </Link>
              </li>
              <li>
                <Link href="#" className="text-[#CCCCCC] hover:text-white transition-colors">
                  Service Status                
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Team</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about-us" className="text-[#CCCCCC] hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact-us" className="text-[#CCCCCC] hover:text-white transition-colors">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">Terms</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/terms-of-use" className="text-[#CCCCCC] hover:text-white transition-colors">
                  Terms of Use
                </Link>
              </li>
              <li>
                <Link href="/privacy-policy" className="text-[#CCCCCC] hover:text-white transition-colors">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-[#333333] pt-8 flex flex-col md:flex-row justify-center items-center">
          <p className="text-[#CCCCCC] mb-4 md:mb-0">© 2025</p>
          
        </div>
      </div>
    </footer>
  )
}
