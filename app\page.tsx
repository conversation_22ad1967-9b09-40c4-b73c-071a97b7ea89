'use client'

import { <PERSON><PERSON> } from "../components/ui/button"
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardHeader } from "../components/ui/card"
import { Check, ChevronDown, Download, X } from "lucide-react"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>pu, <PERSON><PERSON>, GitBranch, Layers, Shield } from "lucide-react"
import { Textarea } from "../components/ui/textarea"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "../components/ui/accordion"
import Link from "next/link"


// Sample AI-generated images for the hero background
const heroImages = [
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/53c0237d-66c6-4f51-bbdd-e7d7b973c95a-0-FkLV3_osY0LlFfeat9oXk.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
  "https://pic.rapidmock.dev/9c340eff-2729-42f7-95a2-364fb43495f1-0-MUgUMIYXa79sWHygkb40x.png",
]


export default function LandingPage() {
  const [currentImages, setCurrentImages] = useState(heroImages)

  const caseStudyImages = [
    'https://pic.rapidmock.dev/a4f5dfde-5836-4a03-87e1-baa511801542-0-TtWsWdQZ0O598UXFZP71N.png',
    'https://pic.rapidmock.dev/53c0237d-66c6-4f51-bbdd-e7d7b973c95a-0-FkLV3_osY0LlFfeat9oXk.png',
    'https://pic.rapidmock.dev/a4f5dfde-5836-4a03-87e1-baa511801542-0-TtWsWdQZ0O598UXFZP71N.png',
    'https://pic.rapidmock.dev/a4f5dfde-5836-4a03-87e1-baa511801542-0-TtWsWdQZ0O598UXFZP71N.png',
    'https://pic.rapidmock.dev/a4f5dfde-5836-4a03-87e1-baa511801542-0-TtWsWdQZ0O598UXFZP71N.png',
    'https://pic.rapidmock.dev/53c0237d-66c6-4f51-bbdd-e7d7b973c95a-0-FkLV3_osY0LlFfeat9oXk.png',
    'https://pic.rapidmock.dev/a4f5dfde-5836-4a03-87e1-baa511801542-0-TtWsWdQZ0O598UXFZP71N.png',
    'https://pic.rapidmock.dev/a4f5dfde-5836-4a03-87e1-baa511801542-0-TtWsWdQZ0O598UXFZP71N.png',
  ]
  // Rotate hero images every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImages((prev) => [...prev.slice(1), prev[0]])
    }, 30000)
    return () => clearInterval(interval)
  }, [])
  return (
    <>
    {/* Hero Section */}
    <section className="relative pt-16 min-h-screen flex items-center overflow-hidden">
        {/* Dynamic Background Grid */}
        <div className="absolute inset-0 grid grid-cols-4 gap-4 p-4 opacity-20">
          {currentImages.map((src, index) => (
            <motion.div
              key={`${src}-${index}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: index * 0.1 }}
              className="relative"
              style={{ height: `${200 + (index % 3) * 50}px` }}
            >
              <img src={src || "/placeholder.svg"} alt="" className="w-full h-full object-cover rounded-lg grayscale" />
            </motion.div>
          ))}
        </div>

        {/* Hero Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">ImageFox - Free AI Image Generator & Photo Editor Online</h1>
            <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">Create Visual Revolutions with Words</h2>
            <p className="text-lg md:text-xl mb-8 max-w-3xl mx-auto leading-relaxed text-[#333333]">
              Input your description, get professional-grade images in 60 seconds - no design skills required.
            </p>
            <div className="space-y-4">
              <Link href="/free-image-generator">
                <Button
                  size="lg"
                  className="cursor-pointer bg-[#121212] text-white hover:bg-[#333333] hover:scale-105 transition-all duration-300 text-lg px-8 py-4"
                >
                  Get Started
                </Button>
              </Link>
              <p className="mt-8 text-sm text-[#666666]">Free trial · No credit card required</p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Value Proposition */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center group"
            >
              <div className="w-12 h-12 mx-auto mb-4 group-hover:scale-105 transition-transform duration-300">
                <Cpu size={48} strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-semibold mb-4">Fast Generation</h3>
              <p className="text-[#666666] leading-relaxed mb-2">512px image &lt;15s</p>
              <p className="text-[#666666] leading-relaxed">Batch processing supported</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center group"
            >
              <div className="w-12 h-12 mx-auto mb-4 group-hover:scale-105 transition-transform duration-300">
                <Palette size={48} strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-semibold mb-4">Precise Control</h3>
              <p className="text-[#666666] leading-relaxed mb-2">Supports style combination instructions</p>
              <p className="text-[#666666] leading-relaxed">e.g. "Monet + Steampunk"</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center group"
            >
              <div className="w-12 h-12 mx-auto mb-4 group-hover:scale-105 transition-transform duration-300">
                <Layers size={48} strokeWidth={2} />
              </div>
              <h3 className="text-2xl font-semibold mb-4">Professional-grade Output</h3>
              <p className="text-[#666666] leading-relaxed mb-2">• 4096×4096 resolution</p>
              <p className="text-[#666666] leading-relaxed">• Commercial license included</p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Real-time Demo */}
      <section className="py-20 bg-[#F5F5F5]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Case Study</h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
            {caseStudyImages.map((src, index) => {
              const height = 200 + (index % 3) * 50
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="col-span-2 md:col-span-1"
                  style={{ height }}
                >
                  <img src={src} alt={`Case Study ${index + 1}`} className="w-full h-full object-cover rounded-lg" />
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Subscription Coming Soon...</h2>
          </motion.div>

        </div>
      </section>

      {/* FAQ */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">FAQ</h2>
          </motion.div>

          <Accordion type="single" collapsible className="space-y-4">
  <AccordionItem value="item-1" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">Who owns the copyright to generated images?</AccordionTrigger>
    <AccordionContent>
      Users have full commercial usage rights to the generated images, which can be used for commercial projects, product designs, and marketing materials.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-2" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">Do you support batch generation via API?</AccordionTrigger>
    <AccordionContent>
      API calls are currently not supported.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-3" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">What is the maximum supported resolution?</AccordionTrigger>
    <AccordionContent>
      Basic plan supports up to 1024px, Pro plan supports up to 2048px, and Enterprise plan supports ultra-HD output up to 4096px.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-4" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">Do you support custom model training?</AccordionTrigger>
    <AccordionContent>
      Custom model training is currently not supported.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-5" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">What factors affect generation speed?</AccordionTrigger>
    <AccordionContent>
      Image complexity, resolution, and current system load. Average generation time: &lt;15 seconds for 512px, &lt;30 seconds for 1024px.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-6" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">Are there any limitations on the free plan?</AccordionTrigger>
    <AccordionContent>
      20 basic resolution images per month, watermark-free and available for commercial use.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-7" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">What happens to unused credits when upgrading a subscription?</AccordionTrigger>
    <AccordionContent>
      Free plan credits will be reset to zero. For paid plans, unused credits will be prorated and carried over to the new billing cycle.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-8" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">What payment methods do you accept?</AccordionTrigger>
    <AccordionContent>
      Credit cards (Visa/Mastercard/Amex) and PayPal.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-9" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">Can I cancel my subscription? What's the refund policy?</AccordionTrigger>
    <AccordionContent>
      You can cancel anytime, and you'll receive a prorated refund for unused months.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-10" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">Do you support image editing/expansion?</AccordionTrigger>
    <AccordionContent>
      Image editing/expansion is currently not supported.
    </AccordionContent>
  </AccordionItem>

  <AccordionItem value="item-11" className="border border-[#E0E0E0] rounded-lg px-6">
    <AccordionTrigger className="text-left">Can it be integrated into design workflows?</AccordionTrigger>
    <AccordionContent>
      Currently not supported.
    </AccordionContent>
  </AccordionItem>
</Accordion>
        </div>
      </section>
    </>
  )
}
