import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";
import { prisma } from "./lib/prisma";
import { compare } from "bcryptjs";
import Github from "next-auth/providers/github";
import { verifyTurnstile } from "./lib/cf/turnstile";

export const { handlers, auth, signIn, signOut} = NextAuth({
    providers: [
        CredentialsProvider({
            name: "Credentials",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
                token: { label: "Token", type: "hidden" },
            },
            async authorize(credentials) {

                if (!credentials?.email || !credentials?.password || !credentials?.token) {
                    return null
                }
                //check turnstile token
                
                try{
                    const isTurnstileValid = await verifyTurnstile(credentials?.token as string)
                    if (!isTurnstileValid) {
                        return null
                    }
                    const user = await prisma.user.findUnique({
                        where: {
                            email: credentials?.email as string,
                        },
                    });
                    if (!user) {
                        return null
                    }
                    const isPasswordValid = await compare(credentials?.password as string, user.passwordHash as string);
                    if (!isPasswordValid) {
                        return null
                    }
                    return user;
                }catch(error){
                    return null
                }
            },
        }),
        Github({
            clientId: process.env.AUTH_GITHUB_ID,
            clientSecret: process.env.AUTH_GITHUB_SECRET,
        })
    ],
    callbacks:{
        signIn: async ({user, account, profile, credentials, email}) => {
            try {
                //check user exist
                const userExist = await prisma.user.findUnique({
                    where: {
                        email: user.email as string,
                    },
                    include: {
                        subscription: true,
                    }
                });

                let isNewUser = false;
                let newUser;
                if (!userExist && account?.type === "oauth") {
                    newUser = await prisma.user.create({
                        data: {
                            email: user.email as string,
                            name: profile?.name as string,
                            image: profile?.image as string,
                        }
                    });
                    await prisma.account.create({
                        data: {
                            userId: newUser.id,
                            type: account?.type as string,
                            provider: account?.provider as string,
                            providerAccountId: account?.providerAccountId as string,
                            access_token: account?.access_token as string,
                            refresh_token: account?.refresh_token as string,
                            expires_at: account?.expires_at as number,
                            token_type: account?.token_type as string,
                            scope: account?.scope as string,
                            id_token: account?.id_token as string,
                            session_state: account?.session_state as string,
                        }
                    });
                    isNewUser = true;
                }
                const userId = isNewUser ? newUser?.id : userExist?.id;
                //如果是email注册第一次登陆或者新的oauth用户没有订阅，就创建一个
                if (isNewUser || (userExist && !userExist.subscription)) { // Corrected logic for existing user without subscription
                    await prisma.subscription.create({
                        data: {
                            userId: userId!, // Should be userExist.id if userExist and not new OAuth
                            type: "FREE",
                            status: "ACTIVE",
                            creditsGrantedPerMonth: 30,
                            creditsRemaining: 30,
                        }
                    });
                } else if (userExist) { // Ensure userExist before trying to update
                    //update user updated
                    await prisma.user.update({
                        where: {
                            email: user.email as string,
                        },
                        data: {
                            updatedAt: new Date(),
                        }
                    });
                }
                return true;
            } catch (error) {
                return false; // Explicitly return false on error to prevent login
            }
        },
        //end
        jwt: async ({ token, user }) => {
            if (user) {
                //find user by email    
                const userExist = await prisma.user.findUnique({
                    where: {
                        email: user.email as string,
                    },
                });
                token.id = userExist?.id as string; 
                token.email = user.email as string;
                token.name = user.name as string;
                token.image = user.image as string;
            }
            return token;
        },
        session: async ({ session, token }) => { 
            if (session.user && token.id) { 
                session.user.id = token.id as string; 
                session.user.email = token.email as string;
                session.user.name = token.name as string;
                session.user.image = token.image as string;
                
                // 获取用户积分和模型信息
                const userWithData = await prisma.user.findUnique({
                    where: {
                        id: token.id as string,
                    },
                    include: {
                        subscription: true,
                    }
                });
                
                if (userWithData && userWithData.subscription) {
                    session.user.credits = userWithData.subscription.creditsRemaining;
                }
                
                // 获取可用的模型列表
                const models = await prisma.model.findMany({
                    where: {
                        category: "TEXT_TO_IMAGE",
                        isAvailable: true
                    }
                });
                
                session.user.models = models;
            }
            return session;
        },
    }
})