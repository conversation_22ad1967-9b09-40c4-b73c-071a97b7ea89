import { signOut, useSession } from "next-auth/react"
import { LogOut, User } from "lucide-react"
import { Button } from "./ui/button"
import Link from "next/link"
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem, DropdownMenuSeparator } from "./ui/dropdown-menu"
import { useEffect, useState } from "react"

export default function UserButton() {
  const session = useSession()
  const [isOpen, setIsOpen] = useState(false)

  // 处理滚动条偏移问题
  useEffect(() => {
    if (isOpen) {
      // 获取当前滚动条宽度
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth

      // 防止页面滚动并保持滚动条空间
      document.body.style.overflow = 'hidden'
      document.body.style.paddingRight = `${scrollbarWidth}px`

      // 为导航栏也添加相同的padding来防止偏移
      const navbar = document.querySelector('nav')
      if (navbar) {
        navbar.style.paddingRight = `${scrollbarWidth}px`
      }
    } else {
      // 恢复正常状态
      document.body.style.overflow = ''
      document.body.style.paddingRight = ''

      const navbar = document.querySelector('nav')
      if (navbar) {
        navbar.style.paddingRight = ''
      }
    }

    // 清理函数
    return () => {
      document.body.style.overflow = ''
      document.body.style.paddingRight = ''

      const navbar = document.querySelector('nav')
      if (navbar) {
        navbar.style.paddingRight = ''
      }
    }
  }, [isOpen])

  return (
    <div className="flex items-center space-x-4">
      {session.data?.user ? (
        <DropdownMenu onOpenChange={setIsOpen}>
            <DropdownMenuTrigger asChild>
                <Button variant="outline" className="cursor-pointer" size="icon">
                    <User size={20} />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem asChild>
                    <Link href="/profile" className="flex items-center w-full">
                        <User size={16} className="mr-2" />
                        Profile
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                    className="cursor-pointer text-red-600 focus:text-red-600"
                    onClick={() => signOut()}
                >
                    <LogOut size={16} className="mr-2" />
                    Sign Out
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <>
        <Link href="/sign-in"><button className="text-black hover:text-[#333333] transition-colors cursor-pointer">Sign In</button></Link>
        <Link href="/sign-up"><Button className="cursor-pointer bg-[#121212] text-white hover:bg-[#333333] hover:scale-105 transition-all duration-300">
          Sign Up
        </Button></Link>
        </>
      )}
    </div>
    )
}