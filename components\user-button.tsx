import { signOut, useSession } from "next-auth/react"
import { LogOut, User } from "lucide-react"
import { But<PERSON> } from "./ui/button"
import Link from "next/link"
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem, DropdownMenuSeparator } from "./ui/dropdown-menu"

export default function UserButton() {
  const session = useSession()

  return (
    <div className="flex items-center space-x-4">
      {session.data?.user ? (
        <DropdownMenu>
            <DropdownMenuTrigger>
                <Button variant="outline" className="cursor-pointer" size="icon">
                    <User size={20} />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
                <DropdownMenuItem>
                    <Link href="/profile">Profile</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="cursor-pointer" onClick={() => signOut()}>
                    Sign Out
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <>
        <Link href="/sign-in"><button className="text-black hover:text-[#333333] transition-colors cursor-pointer">Sign In</button></Link>
        <Link href="/sign-up"><Button className="cursor-pointer bg-[#121212] text-white hover:bg-[#333333] hover:scale-105 transition-all duration-300">
          Sign Up
        </Button></Link>
        </>
      )}
    </div>
    )
}