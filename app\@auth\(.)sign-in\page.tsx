'use client'

import { LoginForm } from "@/components/LoginForm"
import { <PERSON>alog, DialogContent, DialogOverlay, DialogHeader, DialogTitle } from "@/components/ui/dialog" // DialogClose removed if not used for a button
import { useState } from "react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { useSession } from "next-auth/react"

export default function SignInPage() {
    const [open, setOpen] = useState(true)
    const router = useRouter()
    const { data: session } = useSession()

    const handleOpenChange = (isOpen: boolean) => {
        setOpen(isOpen)
        if (!isOpen) {
            router.back()
        }
    }
    useEffect(()=>{
        if (session?.user) {
            router.back()
        }
    },[open, session])

    return (
       <>
      
        <Dialog open={open} onOpenChange={handleOpenChange}>
            <DialogContent>
                <DialogHeader className="flex items-center justify-between">
                    <DialogTitle>Sign In</DialogTitle>
                </DialogHeader>
            <LoginForm /> 
            </DialogContent>
        </Dialog>
        </>
    )
}