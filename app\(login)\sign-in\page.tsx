'use client'

import { LoginForm } from "@/components/LoginForm"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { useEffect } from "react"

const SignInPage = () => {
  const router = useRouter()
  const { data: session } = useSession()
  useEffect(()=>{
    if (session?.user) {
      router.push("/profile")
    }
  },[session])
  
  return (
    <div className="flex flex-col items-center justify-center w-full h-screen gap-y-4">
      <div className="text-2xl font-bold text-center">Sign In</div>
      <LoginForm />
    </div>
  )
}

export default SignInPage