import { DefaultSession } from "next-auth";

// 定义 Model 类型
interface Model {
  id: string;
  modelName: string;
  description: string | null;
  creditCost: number;
}

declare module "next-auth" {
  /**
   * 扩展默认的 Session 类型
   */
  interface Session {
    user: {
      id: string;
      credits?: number;
      models?: Model[];
    } & DefaultSession["user"];
  }

  /**
   * 扩展默认的 User 类型
   */
  interface User {
    id: string;
    credits?: number;
    models?: Model[];
  }
}
