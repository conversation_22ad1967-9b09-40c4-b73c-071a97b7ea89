import ImageGenerator from "../../components/image-generator";
//import { redirect } from "next/navigation";
//import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { Model } from "@/lib/generated/prisma";
import DetailPage from "./DetailPage";

export default async function ImageGeneratorPage() {
  //fetch models
  const models: Model[] = await prisma.model.findMany({
    where: {
      category: "TEXT_TO_IMAGE",
    }
  })

  return (
        <main className="max-w-7xl mx-auto">
          <div className="flex flex-col gap-2 text-center w-2/3 mx-auto mt-36 mb-12">
            <h1 className="text-4xl font-bold">Text to Image Generator For Free</h1>
            <p className="text-gray-600 text-left">Use AI models to generate images from text. This is a text-to-image generation feature, which uses artificial intelligence to generate an image based on the text you input.</p>
          </div>
          <div id="image-generator-form"> {/* Added ID for direct linking */}
            <ImageGenerator userCredits={0} models={models}/>
          </div>
          <DetailPage />
        </main>
    )
}