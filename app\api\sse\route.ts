import { NextRequest } from "next/server";

export async function GET(request: NextRequest) {
    const encoder = new TextEncoder();

    const stream = new ReadableStream({
        start(controller) {
            // 发送初始数据
            controller.enqueue(encoder.encode('data: Initial data\n\n'));

            // 每秒发送一次数据
            setInterval(() => {
                const data = new Date().toISOString();
                const message = `data: ${data}\n\n`;
                controller.enqueue(encoder.encode(message));
            }, 1000);

            // 添加心跳
            setInterval(() => {
                controller.enqueue(encoder.encode(': ping\n\n'));
            }, 30000);
        },
        cancel() {
            console.log('SSE connection closed');
        }
    });

    return new Response(stream, {
        headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'
        }
    });
}