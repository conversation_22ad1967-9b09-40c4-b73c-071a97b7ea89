'use server'
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3'

const client = new S3Client({
  region: 'auto',
  endpoint: 'https://4410ef561e92c5108d521230abedef43.r2.cloudflarestorage.com',
  credentials: {
    //****************************************
    //b900c202d4bed38d13ef9e170e39f189
    //random key ****************************************************************
    accessKeyId: 'b900c202d4bed38d13ef9e170e39f189',
    secretAccessKey: '****************************************************************',
  },
})

export async function uploadToR2(key: string, body: Buffer) {
  const command = new PutObjectCommand({
    Bucket: 'saas-1',
    Key: key,
    ContentType: 'image/png',
    Body: body,
  })

  try {
    const response = await client.send(command)
    if(response.$metadata.httpStatusCode === 200){
      return key
    }
    return null
  } catch (err) {
    console.error(err)
    return null
  }
}

export async function uploadImageFromUrlToR2(key:string,imageUrl: string) {
  try {
    const response = await fetch(imageUrl)
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Extract the file name or generate a unique name
    const url = new URL(imageUrl)
    const pathname = url.pathname
    const fileName = pathname.substring(pathname.lastIndexOf('/') + 1) || `image-${Date.now()}.jpg`

    const newKey = `${key}-${fileName}`
    // Upload to R2
    const uploadResponse = await uploadToR2(newKey, buffer)  
    if (!uploadResponse) {
      throw new Error('Failed to upload image to R2')
    }

    return newKey
  } catch (error) {
    console.error('Error uploading image:', error)
    return null
  }
}