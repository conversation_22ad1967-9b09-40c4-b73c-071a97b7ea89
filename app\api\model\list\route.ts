import { prisma } from "@/lib/prisma";
import { NextRequest } from "next/server";
import { ModelCategory } from "@/lib/generated/prisma";

export async function GET(request: NextRequest) {
  const category = request.nextUrl.searchParams.get('c')
  if (!category) {
    return new Response(JSON.stringify([]));
  } 
  const models = await prisma.model.findMany({
    where: { isAvailable: true, category: category as ModelCategory }
  });

  return new Response(JSON.stringify(models));
}
