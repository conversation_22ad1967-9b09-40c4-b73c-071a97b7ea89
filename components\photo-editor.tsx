'use client'

import { <PERSON>Up, FileUpIcon, Folder<PERSON>pen, Loader2, Upload } from "lucide-react"
import { teardownTraceSubscriber } from "next/dist/build/swc/generated-native"
import Image from "next/image"
import { useEffect, useState } from "react"
import { z } from "zod"
import { toast } from "sonner"
import { Input } from "./ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select"

const modelSchema = z.object({
    prompt: z.string(),
    image_url: z.string(),
    guidance_scale: z.number(),
    num_inference_steps: z.number(),
    num_images: z.number(),
    output_format: z.string(),
    safety_tolerance: z.number(),
    aspect_ratio: z.string(),
    //
    modelId: z.string(),
})

type ModelInput = z.infer<typeof modelSchema>


export default function PhotoEditor() {
    const [image, setImage] = useState<string[] | null>(null);
    const [prompt, setPrompt] = useState<string>('');
    const [userImage, setUserImage] = useState<string>('');

    const [loading, setLoading] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [aspectRatio, setAspectRatio] = useState<string>('match_input_image');
    let timeoutId: NodeJS.Timeout | null = null;

    useEffect(()=>{
        console.log(prompt) 
    }, [prompt])
    async function generateImage() {
        setLoading(true);
        try {
            const result = modelSchema.safeParse({
                prompt: prompt,
                image_url: image ? image[0] : '',
                guidance_scale: 3.5,
                num_inference_steps: 50,
                num_images: 1,
                output_format: "jpeg",
                safety_tolerance: 2,
                aspect_ratio: aspectRatio,
                modelId: "fal-ai/flux-pro/kontext"
            })
            if (!result.success) {
                throw new Error(result.error.message)
            }
            const postData = result.data;
            const response = await fetch('/api/model/editor', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(postData)
            })
            const responseResult = await response.json();
            if(responseResult.status !== 'ok'){
                throw new Error(responseResult.error);
            }
            
            timeoutId = setInterval(async () => {
                const images = await queryTask(responseResult.taskId);
                if(images){
                    setImage(images);
                    setLoading(false);
                    if(timeoutId){
                        clearInterval(timeoutId);
                    }
                }

            }, 5000);
        } catch (error) {
            if(error instanceof Error){
                toast.error(error.message);
            } else {
                toast.error("Something went wrong");
            }
        } finally {
            setLoading(false);
        }
    }

    async function queryTask(taskId: string): Promise<string[] | null> {
        const response = await fetch(`/api/model/editor-task`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ taskId })
        });
        const responseResult = await response.json();
        if(responseResult.status !== 'ok'){
            throw new Error(responseResult.error);
        }
        return responseResult.images.length == 0 ? null : responseResult.images;
    }

    function uploadImage() {
        //点击上传图片
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.click();
        input.onchange = async (e) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            setUploading(true);
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                   //将图片转换为url
                   const url = URL.createObjectURL(file);
                   setImage([url]);
                };
                reader.readAsDataURL(file);
                //upload image to server
                const formData = new FormData();
                formData.append('file', file);
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData,
                });
                const uploadResult = await response.json();
                setUploading(false);
                if(uploadResult.status === 'ok'){
                    console.log('uploadResult.url', uploadResult.url)
                    setUserImage(uploadResult.url);
                } else {
                    toast.error(uploadResult.error);
                }
            }
        };
    }

    return (
        <div>
            <div className="flex flex-col items-center justify-center">
                <div className="relative cursor-pointer w-[600px] h-[600px] flex items-center justify-center rounded-md mx-auto">
                    <img src={image ? image[0] : "/default.svg"} alt="Default upload area" width={600} height={600} />
                    <div className={`absolute inset-0 bg-black/50 flex items-center justify-center ${image ? 'hidden' : ''}`}>
                        <Upload className="h-6 w-6 text-white" />
                    </div>
                    {loading && (
                        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                            <Loader2 className="h-6 w-6 text-white animate-spin" />
                        </div>
                    )}
                </div>
                <div className="flex flex-col items-center justify-center">
                    
                    <div className="flex flex-col">
                        <p className="text-gray-600 text-left">Prompt</p>
                        <div className="flex flex-row items-center justify-between w-[600px]">
                            <textarea value={prompt} onChange={(e) => setPrompt(e.target.value)} className="w-full h-[100px] border border-gray-300 rounded-md p-2 mr-2"></textarea>
                            <button disabled={loading || uploading} onClick={generateImage} className="cursor-pointer bg-blue-500 text-white px-4 py-2 rounded-md mt-2">
                                {loading ? 'Generating...' : 'Generate'}
                            </button>
                        </div>
                    </div>
                    <div className="flex flex-col">
                        <p className="text-gray-600 text-left">Upload your image</p>
                        <div className="flex flex-row items-center justify-between w-[600px] border border-gray-500 rounded-md gap-2 px-2">
                            <Input type="input"
                                className="p-2 border-none outline-none"
                                value={userImage} 
                                onChange={(e) => setUserImage(e.target.value)} 
                            />
                            {uploading ? <Loader2 className="h-6 w-6 animate-spin" /> : <FolderOpen onClick={() => {
                                uploadImage();
                            }} className="cursor-pointer"/>
                            }
                        </div>
                    </div>
                    <div className="flex flex-col">
                        <p className="text-gray-600 text-left">Aspect ratio</p>
                        <div className="flex flex-row items-center justify-between w-[600px] gap-2">
                        <Select name="aspect_ratio" value={aspectRatio} onValueChange={setAspectRatio}>
                            <SelectTrigger className="w-full rounded-sm border border-gray-300  bg-white p-2 focus:outline-none focus:ring disabled:cursor-not-allowed disabled:opacity-50 dark:bg-r8-gray-1">
                                <SelectValue placeholder="选择比例" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="match_input_image">match_input_image</SelectItem>
                                <SelectItem value="1:1">1:1</SelectItem>
                                <SelectItem value="16:9">16:9</SelectItem>
                                <SelectItem value="9:16">9:16</SelectItem>
                                <SelectItem value="4:3">4:3</SelectItem>
                                <SelectItem value="3:4">3:4</SelectItem>
                                <SelectItem value="3:2">3:2</SelectItem>
                                <SelectItem value="2:3">2:3</SelectItem>
                                <SelectItem value="4:5">4:5</SelectItem>
                                <SelectItem value="5:4">5:4</SelectItem>
                                <SelectItem value="21:9">21:9</SelectItem>
                                <SelectItem value="9:21">9:21</SelectItem>
                                <SelectItem value="2:1">2:1</SelectItem>
                                <SelectItem value="1:2">1:2</SelectItem>
                            </SelectContent>
                            </Select>
                        </div>
                    </div>
                </div>  
            </div>
        </div>
    )
}
  