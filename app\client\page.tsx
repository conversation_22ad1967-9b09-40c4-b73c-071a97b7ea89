'use client'

import { useEffect, useState } from "react"

export default function ClientPage() {  

    const [data, setData] = useState<string | null>(null)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        const eventSource = new EventSource('/api/sse')
        
        eventSource.onmessage = (event) => {
            setData(event.data)
            console.log('Received message:', event.data)
        }

        eventSource.onerror = (event: Event) => {
            console.error('SSE error:', event)
            setError('SSE Error: Connection failed')
            eventSource.close()
        }

        eventSource.onopen = () => {
            console.log('SSE connection opened')
        }

        return () => {
            eventSource.close()
            console.log('SSE connection closed')
        }
    }, [])
    return (
        <div>
            <h1>SSE Client</h1>
            <p>{data || 'No data received yet.'}</p>
            <p>{error || 'No error occurred.'}</p>
        </div>
    )
}