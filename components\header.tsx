"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, User, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { signOut, useSession } from "next-auth/react"
import Image from "next/image"
  
export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const session = useSession()

  return (
    <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-[#E0E0E0] z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="w-8 h-8 mr-3">
              {/* <svg viewBox="0 0 32 32" className="w-full h-full stroke-black stroke-2 fill-none">
                <circle cx="16" cy="8" r="3" />
                <circle cx="8" cy="20" r="3" />
                <circle cx="24" cy="20" r="3" />
                <path d="M16 11L13 17M16 11L19 17M11 20L13 17M21 20L19 17M13 17H19" />
              </svg> */}
              <Image src="/logo.svg" alt="Logo" width={32} height={32} />
            </div>
            <Link href="/"><span className="text-xl font-semibold">ImageFox</span></Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/free-image-generator" className="text-black hover:text-[#333333] transition-colors">
              Free Image Generator
            </Link>
            <Link href="#" className="text-black hover:text-[#333333] transition-colors">
              Photo Editor
            </Link>
            <Link href="#features" className="text-black hover:text-[#333333] transition-colors">
              Features
            </Link>
            <Link href="#cases" className="text-black hover:text-[#333333] transition-colors">
              Cases
            </Link>
            <Link href="#pricing" className="text-black hover:text-[#333333] transition-colors">
              Pricing
            </Link>
            <Link href="#faq" className="text-black hover:text-[#333333] transition-colors">
              FAQ
            </Link>
          </div>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {session.data?.user ? (
              <Link href="/profile"><User size={20} /></Link>
            ) : (
              <>
              <Link href="/sign-in"><button className="text-black hover:text-[#333333] transition-colors cursor-pointer">Sign In</button></Link>
              <Link href="/sign-up"><Button className="cursor-pointer bg-[#121212] text-white hover:bg-[#333333] hover:scale-105 transition-all duration-300">
                Sign Up
              </Button></Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <button className="md:hidden" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: "-100%" }} // 从屏幕顶部外开始
            animate={{ opacity: 1, y: 0 }}       // 滑动到屏幕内
            exit={{ opacity: 0, y: "-100%" }}    // 滑出到屏幕顶部外
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="md:hidden fixed inset-0 bg-white z-[60] overflow-y-auto" // 提高 z-index (例如 z-60, 高于 Header 的 z-50) 使其覆盖 Header，并移除 pt-16
          >
            {/* 菜单内部的头部，包含关闭按钮 */}
            <div className="flex justify-end items-center h-16 px-4 sm:px-6 lg:px-8 border-b border-[#E0E0E0]">
              {/* 你可以在这里放置 Logo 或菜单标题，如果需要的话 */}
              {/* <div className="flex items-center">ImageFox Menu</div> */}
              <button onClick={() => setMobileMenuOpen(false)} className="p-2"> {/* 为按钮增加一些内边距，方便点击 */}
                <X size={24} />
              </button>
            </div>
            {/* 菜单链接内容 */}
            <div className="px-4 py-4 space-y-4"> {/* 这里的内边距可以根据你的设计调整 */}
              <Link href="/free-image-generator" onClick={() => setMobileMenuOpen(false)} className="block text-black hover:text-[#333333] py-2">
                Free Image Generator
              </Link>
              <Link href="/photo-editor" onClick={() => setMobileMenuOpen(false)} className="block text-black hover:text-[#333333] py-2">
                Photo Editor
              </Link>
              <Link href="/#features" onClick={() => setMobileMenuOpen(false)} className="block text-black hover:text-[#333333] py-2">
                Features
              </Link>
              <Link href="/#cases" onClick={() => setMobileMenuOpen(false)} className="block text-black hover:text-[#333333] py-2">
                Cases
              </Link>
              <Link href="/#pricing" onClick={() => setMobileMenuOpen(false)} className="block text-black hover:text-[#333333] py-2">
                Pricing
              </Link>
              <Link href="/#faq" onClick={() => setMobileMenuOpen(false)} className="block text-black hover:text-[#333333] py-2">
                FAQ
              </Link>
              <div className="pt-4 border-t border-[#E0E0E0] space-y-2">
                {session.data?.user ? (
                  <>
                  <Link href="/profile" onClick={() => setMobileMenuOpen(false)} className="block text-black hover:text-[#333333] py-2">
                    Profile
                  </Link>
                  {/* 将 Sign Out 改为 button，点击后执行 signOut 和关闭菜单 */}
                  <button onClick={() => { signOut(); setMobileMenuOpen(false); }} className="block w-full text-left text-black hover:text-[#333333] py-2">
                    Sign Out
                  </button>
                  </>
                ) : (
                  <>
                  <Link href="/sign-in" onClick={() => setMobileMenuOpen(false)}><button className="block w-full text-left text-black hover:text-[#333333] py-2">Sign In</button></Link>
                  <Link href="/sign-up" onClick={() => setMobileMenuOpen(false)}><Button className="w-full bg-[#121212] text-white hover:bg-[#333333] mt-2">Sign Up</Button></Link>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  )
}
