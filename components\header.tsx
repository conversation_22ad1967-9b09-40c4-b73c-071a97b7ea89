"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, User, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { signOut, useSession } from "next-auth/react"
import Image from "next/image"

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const session = useSession()

  return (
    <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-[#E0E0E0] z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="w-8 h-8 mr-3">
              {/* <svg viewBox="0 0 32 32" className="w-full h-full stroke-black stroke-2 fill-none">
                <circle cx="16" cy="8" r="3" />
                <circle cx="8" cy="20" r="3" />
                <circle cx="24" cy="20" r="3" />
                <path d="M16 11L13 17M16 11L19 17M11 20L13 17M21 20L19 17M13 17H19" />
              </svg> */}
              <Image src="/logo.svg" alt="Logo" width={32} height={32} />
            </div>
            <Link href="/"><span className="text-xl font-semibold">ImageFox</span></Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/free-image-generator" className="text-black hover:text-[#333333] transition-colors">
              Free Image Generator
            </Link>
            <Link href="#" className="text-black hover:text-[#333333] transition-colors">
              Photo Editor
            </Link>
            <Link href="#features" className="text-black hover:text-[#333333] transition-colors">
              Features
            </Link>
            <Link href="#cases" className="text-black hover:text-[#333333] transition-colors">
              Cases
            </Link>
            <Link href="#pricing" className="text-black hover:text-[#333333] transition-colors">
              Pricing
            </Link>
            <Link href="#faq" className="text-black hover:text-[#333333] transition-colors">
              FAQ
            </Link>
          </div>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {session.data?.user ? (
              <Link href="/profile"><User size={20} /></Link>
            ) : (
              <>
              <Link href="/sign-in"><button className="text-black hover:text-[#333333] transition-colors cursor-pointer">Sign In</button></Link>
              <Link href="/sign-up"><Button className="cursor-pointer bg-[#121212] text-white hover:bg-[#333333] hover:scale-105 transition-all duration-300">
                Sign Up
              </Button></Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <button className="md:hidden" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: "-100%" }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: "-100%" }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="md:hidden fixed inset-0 bg-white z-[60] overflow-y-auto"
          >
            {/* 菜单内部的头部，包含Logo和关闭按钮 */}
            <div className="flex justify-between items-center h-16 px-4 sm:px-6 lg:px-8 border-b border-[#E0E0E0]">
              <div className="flex items-center">
                <div className="w-8 h-8 mr-3">
                  <Image src="/logo.svg" alt="Logo" width={32} height={32} />
                </div>
                <span className="text-xl font-semibold">ImageFox</span>
              </div>
              <button
                onClick={() => setMobileMenuOpen(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                aria-label="关闭菜单"
              >
                <X size={24} />
              </button>
            </div>

            {/* 菜单链接内容 */}
            <div className="px-4 py-6 space-y-1">
              <Link
                href="/free-image-generator"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg text-black hover:text-[#333333] hover:bg-gray-50 py-3 px-2 rounded-lg transition-all"
              >
                Free Image Generator
              </Link>
              <Link
                href="#"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg text-black hover:text-[#333333] hover:bg-gray-50 py-3 px-2 rounded-lg transition-all"
              >
                Photo Editor
              </Link>
              <Link
                href="#features"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg text-black hover:text-[#333333] hover:bg-gray-50 py-3 px-2 rounded-lg transition-all"
              >
                Features
              </Link>
              <Link
                href="#cases"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg text-black hover:text-[#333333] hover:bg-gray-50 py-3 px-2 rounded-lg transition-all"
              >
                Cases
              </Link>
              <Link
                href="#pricing"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg text-black hover:text-[#333333] hover:bg-gray-50 py-3 px-2 rounded-lg transition-all"
              >
                Pricing
              </Link>
              <Link
                href="#faq"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg text-black hover:text-[#333333] hover:bg-gray-50 py-3 px-2 rounded-lg transition-all"
              >
                FAQ
              </Link>

              {/* 认证按钮区域 */}
              <div className="pt-6 mt-6 border-t border-[#E0E0E0] space-y-2">
                {session.data?.user ? (
                  <>
                    <Link
                      href="/profile"
                      onClick={() => setMobileMenuOpen(false)}
                      className="block text-lg text-black hover:text-[#333333] hover:bg-gray-50 py-3 px-2 rounded-lg transition-all"
                    >
                      Profile
                    </Link>
                    <button
                      onClick={() => {
                        signOut();
                        setMobileMenuOpen(false);
                      }}
                      className="block w-full text-left text-lg text-black hover:text-[#333333] hover:bg-gray-50 py-3 px-2 rounded-lg transition-all"
                    >
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <Link
                      href="/sign-in"
                      onClick={() => setMobileMenuOpen(false)}
                      className="block text-lg text-black hover:text-[#333333] hover:bg-gray-50 py-3 px-2 rounded-lg transition-all"
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/sign-up"
                      onClick={() => setMobileMenuOpen(false)}
                      className="block mt-4"
                    >
                      <Button className="w-full bg-[#121212] text-white hover:bg-[#333333] py-3 text-lg">
                        Sign Up
                      </Button>
                    </Link>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  )
}
