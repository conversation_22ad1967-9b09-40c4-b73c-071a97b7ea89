"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, User, X, LogOut } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { signOut, useSession } from "next-auth/react"
import Image from "next/image"
import UserButton from "./user-button"

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const session = useSession()

  return (
    <nav className="fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-[#E0E0E0] z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="w-8 h-8 mr-3">
              {/* <svg viewBox="0 0 32 32" className="w-full h-full stroke-black stroke-2 fill-none">
                <circle cx="16" cy="8" r="3" />
                <circle cx="8" cy="20" r="3" />
                <circle cx="24" cy="20" r="3" />
                <path d="M16 11L13 17M16 11L19 17M11 20L13 17M21 20L19 17M13 17H19" />
              </svg> */}
              <Image src="/logo.svg" alt="Logo" width={32} height={32} />
            </div>
            <Link href="/"><span className="text-xl font-semibold">ImageFox</span></Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/free-image-generator" className="text-black hover:text-[#333333] transition-colors">
              Free Image Generator
            </Link>
            <Link href="#" className="text-black hover:text-[#333333] transition-colors">
              Photo Editor
            </Link>
            <Link href="#features" className="text-black hover:text-[#333333] transition-colors">
              Features
            </Link>
            <Link href="#cases" className="text-black hover:text-[#333333] transition-colors">
              Cases
            </Link>
            <Link href="#pricing" className="text-black hover:text-[#333333] transition-colors">
              Pricing
            </Link>
            <Link href="#faq" className="text-black hover:text-[#333333] transition-colors">
              FAQ
            </Link>
          </div>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <UserButton />
          </div>

          {/* Mobile menu button */}
          <button className="md:hidden" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <div className="md:hidden flex flex-col min-h-screen">  
            {/* 主要导航菜单 */}
            <div className="flex-1 overflow-y-auto px-4 py-6 space-y-2">
              <Link
                href="/free-image-generator"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg font-medium text-black hover:text-[#333333] hover:bg-gray-50 py-4 px-3 rounded-lg transition-all"
              >
                Free Image Generator
              </Link>
              <Link
                href="#"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg font-medium text-black hover:text-[#333333] hover:bg-gray-50 py-4 px-3 rounded-lg transition-all"
              >
                Photo Editor
              </Link>
              <Link
                href="#features"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg font-medium text-black hover:text-[#333333] hover:bg-gray-50 py-4 px-3 rounded-lg transition-all"
              >
                Features
              </Link>
              <Link
                href="#cases"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg font-medium text-black hover:text-[#333333] hover:bg-gray-50 py-4 px-3 rounded-lg transition-all"
              >
                Cases
              </Link>
              <Link
                href="#pricing"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg font-medium text-black hover:text-[#333333] hover:bg-gray-50 py-4 px-3 rounded-lg transition-all"
              >
                Pricing
              </Link>
              <Link
                href="#faq"
                onClick={() => setMobileMenuOpen(false)}
                className="block text-lg font-medium text-black hover:text-[#333333] hover:bg-gray-50 py-4 px-3 rounded-lg transition-all"
              >
                FAQ
              </Link>
              {session.data?.user ? (
                <>
                  <Link
                    href="/profile"
                    onClick={() => setMobileMenuOpen(false)}
                    className="flex items-center text-lg font-medium text-black hover:text-[#333333] hover:bg-white py-4 px-3 rounded-lg transition-all"
                  >
                    <User size={20} className="mr-3" />
                    Profile
                  </Link>
                  <button
                    onClick={() => {
                      signOut();
                      setMobileMenuOpen(false);
                    }}
                    className="flex items-center w-full text-left text-lg font-medium text-red-600 hover:text-red-700 hover:bg-white py-4 px-3 rounded-lg transition-all"
                  >
                    <X size={20} className="mr-3" />
                    Sign Out
                  </button>
                </>
              ) : (
                  <>
                  <Link
                    href="/sign-in"
                    onClick={() => setMobileMenuOpen(false)}
                    className="block text-lg font-medium text-black hover:text-[#333333] hover:bg-white py-4 px-3 rounded-lg transition-all text-center"
                  >
                    Sign In
                  </Link>
                  <Link
                    href="/sign-up"
                    onClick={() => setMobileMenuOpen(false)}
                    className="block"
                  >
                    <Button className="w-full bg-[#121212] text-white hover:bg-[#333333] py-4 text-lg font-medium rounded-lg">
                      Sign Up
                    </Button>
                  </Link>
                  </>
              )}
            </div>

            {/* 认证按钮区域 - 固定在底部 */}
            <div className="flex-shrink-0 px-4 py-6 border-t border-[#E0E0E0] bg-gray-50/50">
              
            </div>
          </div>
        )}
      </AnimatePresence>
    </nav>
  )
}
